from aws_cdk import CfnOutput, Duration, RemovalPolicy
from aws_cdk import aws_iam as iam
from aws_cdk import aws_s3 as s3
from constructs import Construct

from .config import (
    ALLOWED_FILE_EXTENSIONS,
    DEFAULT_ALLOWED_ORIGINS,
    S3_CORS_EXPOSED_HEADERS,
    S3_CORS_MAX_AGE_SECONDS,
    S3_MULTIPART_UPLOAD_CLEANUP_DAYS,
)


class S3StorageConstruct(Construct):
    """Construct for S3 bucket to store EDF files with proper configurations"""

    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        bucket_name: str | None = None,
        allowed_origins: list[str] | None = None,
    ) -> None:
        super().__init__(scope, construct_id)

        self.allowed_origins = allowed_origins or DEFAULT_ALLOWED_ORIGINS
        self._create_s3_bucket(bucket_name)
        self._create_backend_role()
        self._grant_permissions()
        self._create_outputs()

    def _create_s3_bucket(self, bucket_name: str | None) -> None:
        """Create S3 bucket with security and lifecycle configurations"""
        self.edf_bucket = s3.Bucket(
            self,
            "EDFStorageBucket",
            bucket_name=bucket_name,
            versioned=True,
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            lifecycle_rules=self._get_lifecycle_rules(),
            cors=self._get_cors_rules(),
            removal_policy=RemovalPolicy.RETAIN,
        )

    def _get_lifecycle_rules(self) -> list[s3.LifecycleRule]:
        """Get S3 lifecycle rules configuration"""
        return [
            s3.LifecycleRule(
                id="DeleteIncompleteMultipartUploads",
                abort_incomplete_multipart_upload_after=Duration.days(
                    S3_MULTIPART_UPLOAD_CLEANUP_DAYS
                ),
            )
        ]

    def _get_cors_rules(self) -> list[s3.CorsRule]:
        """Get S3 CORS configuration"""
        return [
            s3.CorsRule(
                allowed_methods=[
                    s3.HttpMethods.GET,
                    s3.HttpMethods.PUT,
                    s3.HttpMethods.POST,
                    s3.HttpMethods.HEAD,
                ],
                allowed_origins=self.allowed_origins,
                allowed_headers=["*"],
                exposed_headers=S3_CORS_EXPOSED_HEADERS,
                max_age=S3_CORS_MAX_AGE_SECONDS,
            )
        ]

    def _create_backend_role(self) -> None:
        """Create IAM role for backend service"""
        self.backend_role = iam.Role(
            self,
            "BackendServiceRole",
            assumed_by=iam.ServicePrincipal("ec2.amazonaws.com"),
            description="Role for backend service to access S3 for EDF file operations",
        )

    def _grant_permissions(self) -> None:
        """Grant necessary permissions to backend role"""
        self.edf_bucket.grant_read_write(self.backend_role)
        self._add_presigned_url_policy()

    def _add_presigned_url_policy(self) -> None:
        """Add policy for presigned URL generation"""
        presigned_url_policy = iam.PolicyStatement(
            effect=iam.Effect.ALLOW,
            actions=[
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket",
            ],
            resources=[self.edf_bucket.bucket_arn, f"{self.edf_bucket.bucket_arn}/*"],
            conditions={"StringLike": {"s3:prefix": ALLOWED_FILE_EXTENSIONS}},
        )
        self.backend_role.add_to_policy(presigned_url_policy)

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs"""
        CfnOutput(
            self,
            "BucketName",
            value=self.edf_bucket.bucket_name,
            description="Name of the S3 bucket for EDF file storage",
        )

        CfnOutput(
            self,
            "BucketArn",
            value=self.edf_bucket.bucket_arn,
            description="ARN of the S3 bucket",
        )

        CfnOutput(
            self,
            "BackendRoleArn",
            value=self.backend_role.role_arn,
            description="ARN of the backend service role",
        )
