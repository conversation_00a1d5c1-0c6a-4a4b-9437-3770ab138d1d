from aws_cdk import Stack, Tags
from constructs import Construct

from .config import ENVIRONMENT, PROJECT_NAME, PURPOSE
from .s3_construct import S3StorageConstruct


class InfraStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        S3StorageConstruct(self, "S3Storage", bucket_name=None, allowed_origins=None)

        Tags.of(self).add("Project", PROJECT_NAME)
        Tags.of(self).add("Environment", ENVIRONMENT)
        Tags.of(self).add("Purpose", PURPOSE)
