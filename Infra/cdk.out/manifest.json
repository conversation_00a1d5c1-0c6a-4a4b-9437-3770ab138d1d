{"version": "48.0.0", "artifacts": {"BiormikaStack.assets": {"type": "cdk:asset-manifest", "properties": {"file": "BiormikaStack.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "BiormikaStack": {"type": "aws:cloudformation:stack", "environment": "aws://unknown-account/us-east-1", "properties": {"templateFile": "BiormikaStack.template.json", "terminationProtection": false, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-deploy-role-${AWS::AccountId}-us-east-1", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-cfn-exec-role-${AWS::AccountId}-us-east-1", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-us-east-1/82c0b923dd4a81b7856be42397e6d8ec7faac60bb0b082e8c44c610703352897.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["BiormikaStack.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-lookup-role-${AWS::AccountId}-us-east-1", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["BiormikaStack.assets"], "metadata": {"/BiormikaStack/S3Storage/EDFStorageBucket": [{"type": "aws:cdk:analytics:construct", "data": {"blockPublicAccess": "*", "cors": [{"allowedMethods": ["GET", "PUT", "POST", "HEAD"], "allowedOrigins": "*", "allowedHeaders": "*", "exposedHeaders": "*", "maxAge": "*"}], "encryption": "S3_MANAGED", "lifecycleRules": [{"abortIncompleteMultipartUploadAfter": "*", "id": "*"}], "removalPolicy": "retain", "versioned": true}}, {"type": "aws:cdk:analytics:method", "data": {"addCorsRule": [{"allowedMethods": ["GET", "PUT", "POST", "HEAD"], "allowedOrigins": "*", "allowedHeaders": "*", "exposedHeaders": "*", "maxAge": "*"}, "*", "*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addLifecycleRule": [{"abortIncompleteMultipartUploadAfter": "*", "id": "*"}, "*", "*"]}}], "/BiormikaStack/S3Storage/EDFStorageBucket/Resource": [{"type": "aws:cdk:logicalId", "data": "S3StorageEDFStorageBucket8B55A415"}], "/BiormikaStack/S3Storage/BackendServiceRole": [{"type": "aws:cdk:analytics:construct", "data": {"assumedBy": {"principalAccount": "*", "assumeRoleAction": "*"}, "description": "*"}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachInlinePolicy": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPolicy": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addToPrincipalPolicy": [{}]}}], "/BiormikaStack/S3Storage/BackendServiceRole/ImportBackendServiceRole": [{"type": "aws:cdk:analytics:construct", "data": "*"}], "/BiormikaStack/S3Storage/BackendServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "S3StorageBackendServiceRole3F93CE36"}], "/BiormikaStack/S3Storage/BackendServiceRole/DefaultPolicy": [{"type": "aws:cdk:analytics:construct", "data": "*"}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"attachToRole": ["*"]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}, {"type": "aws:cdk:analytics:method", "data": {"addStatements": [{}]}}], "/BiormikaStack/S3Storage/BackendServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "S3StorageBackendServiceRoleDefaultPolicyA3A2409B"}], "/BiormikaStack/S3Storage/BucketName": [{"type": "aws:cdk:logicalId", "data": "S3StorageBucketNameCDDE28C1"}], "/BiormikaStack/S3Storage/BucketArn": [{"type": "aws:cdk:logicalId", "data": "S3StorageBucketArn7F09995F"}], "/BiormikaStack/S3Storage/BackendRoleArn": [{"type": "aws:cdk:logicalId", "data": "S3StorageBackendRoleArn5AA6816A"}], "/BiormikaStack/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/BiormikaStack/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/BiormikaStack/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "BiormikaStack"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}, "aws-cdk-lib/feature-flag-report": {"type": "cdk:feature-flag-report", "properties": {"module": "aws-cdk-lib", "flags": {"@aws-cdk/aws-signer:signingProfileNamePassedToCfn": {"recommendedValue": true, "explanation": "Pass signingProfileName to CfnSigningProfile"}, "@aws-cdk/core:newStyleStackSynthesis": {"recommendedValue": true, "explanation": "Switch to new stack synthesis method which enables CI/CD", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/core:stackRelativeExports": {"recommendedValue": true, "explanation": "Name exports based on the construct paths relative to the stack, rather than the global construct path", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/aws-ecs-patterns:secGroupsDisablesImplicitOpenListener": {"recommendedValue": true, "explanation": "Disable implicit openListener when custom security groups are provided"}, "@aws-cdk/aws-rds:lowercaseDbIdentifier": {"recommendedValue": true, "explanation": "Force lowercasing of RDS Cluster names in CDK", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/aws-apigateway:usagePlanKeyOrderInsensitiveId": {"recommendedValue": true, "explanation": "Allow adding/removing multiple UsagePlanKeys independently", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/aws-lambda:recognizeVersionProps": {"recommendedValue": true, "explanation": "Enable this feature flag to opt in to the updated logical id calculation for Lambda Version created using the  `fn.currentVersion`.", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/aws-lambda:recognizeLayerVersion": {"userValue": true, "recommendedValue": true, "explanation": "Enable this feature flag to opt in to the updated logical id calculation for Lambda Version created using the `fn.currentVersion`."}, "@aws-cdk/aws-cloudfront:defaultSecurityPolicyTLSv1.2_2021": {"recommendedValue": true, "explanation": "Enable this feature flag to have cloudfront distributions use the security policy TLSv1.2_2021 by default.", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/core:checkSecretUsage": {"userValue": true, "recommendedValue": true, "explanation": "Enable this flag to make it impossible to accidentally use SecretValues in unsafe locations"}, "@aws-cdk/core:target-partitions": {"userValue": ["aws", "aws-cn"], "recommendedValue": ["aws", "aws-cn"], "explanation": "What regions to include in lookup tables of environment agnostic stacks"}, "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": {"userValue": true, "recommendedValue": true, "explanation": "ECS extensions will automatically add an `awslogs` driver if no logging is specified"}, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": {"userValue": true, "recommendedValue": true, "explanation": "Enable this feature flag to have Launch Templates generated by the `InstanceRequireImdsv2Aspect` use unique names."}, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": {"userValue": true, "recommendedValue": true, "explanation": "ARN format used by ECS. In the new ARN format, the cluster name is part of the resource ID."}, "@aws-cdk/aws-iam:minimizePolicies": {"userValue": true, "recommendedValue": true, "explanation": "Minimize IAM policies by combining Statements"}, "@aws-cdk/core:validateSnapshotRemovalPolicy": {"userValue": true, "recommendedValue": true, "explanation": "Error on snapshot removal policies on resources that do not support it."}, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": {"userValue": true, "recommendedValue": true, "explanation": "Generate key aliases that include the stack name"}, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": {"userValue": true, "recommendedValue": true, "explanation": "Enable this feature flag to create an S3 bucket policy by default in cases where an AWS service would automatically create the Policy if one does not exist."}, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": {"userValue": true, "recommendedValue": true, "explanation": "Restrict KMS key policy for encrypted Queues a bit more"}, "@aws-cdk/aws-apigateway:disableCloudWatchRole": {"userValue": true, "recommendedValue": true, "explanation": "Make default CloudWatch Role behavior safe for multiple API Gateways in one environment"}, "@aws-cdk/core:enablePartitionLiterals": {"userValue": true, "recommendedValue": true, "explanation": "Make ARNs concrete if AWS partition is known"}, "@aws-cdk/aws-events:eventsTargetQueueSameAccount": {"userValue": true, "recommendedValue": true, "explanation": "Event Rules may only push to encrypted SQS queues in the same account"}, "@aws-cdk/aws-ecs:disableExplicitDeploymentControllerForCircuitBreaker": {"userValue": true, "recommendedValue": true, "explanation": "Avoid setting the \"ECS\" deployment controller when adding a circuit breaker"}, "@aws-cdk/aws-iam:importedRoleStackSafeDefaultPolicyName": {"userValue": true, "recommendedValue": true, "explanation": "Enable this feature to by default create default policy names for imported roles that depend on the stack the role is in."}, "@aws-cdk/aws-s3:serverAccessLogsUseBucketPolicy": {"userValue": true, "recommendedValue": true, "explanation": "Use S3 Bucket Policy instead of ACLs for Server Access Logging"}, "@aws-cdk/aws-route53-patters:useCertificate": {"userValue": true, "recommendedValue": true, "explanation": "Use the official `Certificate` resource instead of `DnsValidatedCertificate`"}, "@aws-cdk/customresources:installLatestAwsSdkDefault": {"userValue": false, "recommendedValue": false, "explanation": "Whether to install the latest SDK by default in AwsCustomResource"}, "@aws-cdk/aws-rds:databaseProxyUniqueResourceName": {"userValue": true, "recommendedValue": true, "explanation": "Use unique resource name for Database Proxy"}, "@aws-cdk/aws-codedeploy:removeAlarmsFromDeploymentGroup": {"userValue": true, "recommendedValue": true, "explanation": "Remove CloudWatch alarms from deployment group"}, "@aws-cdk/aws-apigateway:authorizerChangeDeploymentLogicalId": {"userValue": true, "recommendedValue": true, "explanation": "Include authorizer configuration in the calculation of the API deployment logical ID."}, "@aws-cdk/aws-ec2:launchTemplateDefaultUserData": {"userValue": true, "recommendedValue": true, "explanation": "Define user data for a launch template by default when a machine image is provided."}, "@aws-cdk/aws-secretsmanager:useAttachedSecretResourcePolicyForSecretTargetAttachments": {"userValue": true, "recommendedValue": true, "explanation": "SecretTargetAttachments uses the ResourcePolicy of the attached Secret."}, "@aws-cdk/aws-redshift:columnId": {"userValue": true, "recommendedValue": true, "explanation": "Whether to use an ID to track Redshift column changes"}, "@aws-cdk/aws-stepfunctions-tasks:enableEmrServicePolicyV2": {"userValue": true, "recommendedValue": true, "explanation": "Enable AmazonEMRServicePolicy_v2 managed policies"}, "@aws-cdk/aws-ec2:restrictDefaultSecurityGroup": {"userValue": true, "recommendedValue": true, "explanation": "Restrict access to the VPC default security group"}, "@aws-cdk/aws-apigateway:requestValidatorUniqueId": {"userValue": true, "recommendedValue": true, "explanation": "Generate a unique id for each RequestValidator added to a method"}, "@aws-cdk/aws-kms:aliasNameRef": {"userValue": true, "recommendedValue": true, "explanation": "KMS Alias name and keyArn will have implicit reference to KMS Key"}, "@aws-cdk/aws-kms:applyImportedAliasPermissionsToPrincipal": {"userValue": true, "recommendedValue": true, "explanation": "Enable grant methods on Aliases imported by name to use kms:ResourceAliases condition"}, "@aws-cdk/aws-autoscaling:generateLaunchTemplateInsteadOfLaunchConfig": {"userValue": true, "recommendedValue": true, "explanation": "Generate a launch template when creating an AutoScalingGroup"}, "@aws-cdk/core:includePrefixInUniqueNameGeneration": {"userValue": true, "recommendedValue": true, "explanation": "Include the stack prefix in the stack name generation process"}, "@aws-cdk/aws-efs:denyAnonymousAccess": {"userValue": true, "recommendedValue": true, "explanation": "EFS denies anonymous clients accesses"}, "@aws-cdk/aws-opensearchservice:enableOpensearchMultiAzWithStandby": {"userValue": true, "recommendedValue": true, "explanation": "Enables support for Multi-AZ with Standby deployment for opensearch domains"}, "@aws-cdk/aws-lambda-nodejs:useLatestRuntimeVersion": {"userValue": true, "recommendedValue": true, "explanation": "Enables aws-lambda-nodejs.Function to use the latest available NodeJs runtime as the default"}, "@aws-cdk/aws-efs:mountTargetOrderInsensitiveLogicalId": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, mount targets will have a stable logicalId that is linked to the associated subnet."}, "@aws-cdk/aws-rds:auroraClusterChangeScopeOfInstanceParameterGroupWithEachParameters": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, a scope of InstanceParameterGroup for AuroraClusterInstance with each parameters will change."}, "@aws-cdk/aws-appsync:useArnForSourceApiAssociationIdentifier": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, will always use the arn for identifiers for CfnSourceApiAssociation in the GraphqlApi construct rather than id."}, "@aws-cdk/aws-rds:preventRenderingDeprecatedCredentials": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, creating an RDS database cluster from a snapshot will only render credentials for snapshot credentials."}, "@aws-cdk/aws-codepipeline-actions:useNewDefaultBranchForCodeCommitSource": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the CodeCommit source action is using the default branch name 'main'."}, "@aws-cdk/aws-cloudwatch-actions:changeLambdaPermissionLogicalIdForLambdaAction": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the logical ID of a Lambda permission for a Lambda action includes an alarm ID."}, "@aws-cdk/aws-codepipeline:crossAccountKeysDefaultValueToFalse": {"userValue": true, "recommendedValue": true, "explanation": "Enables Pipeline to set the default value for crossAccountKeys to false."}, "@aws-cdk/aws-codepipeline:defaultPipelineTypeToV2": {"userValue": true, "recommendedValue": true, "explanation": "Enables Pipeline to set the default pipeline type to V2."}, "@aws-cdk/aws-kms:reduceCrossAccountRegionPolicyScope": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, IAM Policy created from KMS key grant will reduce the resource scope to this key only."}, "@aws-cdk/pipelines:reduceAssetRoleTrustScope": {"recommendedValue": true, "explanation": "Remove the root account principal from PipelineAssetsFileRole trust policy", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/aws-eks:nodegroupNameAttribute": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, nodegroupName attribute of the provisioned EKS NodeGroup will not have the cluster name prefix."}, "@aws-cdk/aws-ec2:ebsDefaultGp3Volume": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the default volume type of the EBS volume will be GP3"}, "@aws-cdk/aws-ecs:removeDefaultDeploymentAlarm": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, remove default deployment alarm settings"}, "@aws-cdk/custom-resources:logApiResponseDataPropertyTrueDefault": {"userValue": false, "recommendedValue": false, "explanation": "When enabled, the custom resource used for `AwsCustomResource` will configure the `logApiResponseData` property as true by default"}, "@aws-cdk/aws-s3:keepNotificationInImportedBucket": {"userValue": false, "recommendedValue": false, "explanation": "When enabled, Adding notifications to a bucket in the current stack will not remove notification from imported stack."}, "@aws-cdk/aws-stepfunctions-tasks:useNewS3UriParametersForBedrockInvokeModelTask": {"recommendedValue": true, "explanation": "When enabled, use new props for S3 URI field in task definition of state machine for bedrock invoke model.", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/core:explicitStackTags": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, stack tags need to be assigned explicitly on a Stack."}, "@aws-cdk/aws-ecs:enableImdsBlockingDeprecatedFeature": {"userValue": false, "recommendedValue": false, "explanation": "When set to true along with canContainersAccessInstanceRole=false in ECS cluster, new updated commands will be added to UserData to block container accessing IMDS. **Applicable to Linux only. IMPORTANT: See [details.](#aws-cdkaws-ecsenableImdsBlockingDeprecatedFeature)**"}, "@aws-cdk/aws-ecs:disableEcsImdsBlocking": {"userValue": true, "recommendedValue": true, "explanation": "When set to true, CDK synth will throw exception if canContainersAccessInstanceRole is false. **IMPORTANT: See [details.](#aws-cdkaws-ecsdisableEcsImdsBlocking)**"}, "@aws-cdk/aws-ecs:reduceEc2FargateCloudWatchPermissions": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, we will only grant the necessary permissions when users specify cloudwatch log group through logConfiguration"}, "@aws-cdk/aws-dynamodb:resourcePolicyPerReplica": {"userValue": true, "recommendedValue": true, "explanation": "When enabled will allow you to specify a resource policy per replica, and not copy the source table policy to all replicas"}, "@aws-cdk/aws-ec2:ec2SumTImeoutEnabled": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, initOptions.timeout and resourceSignalTimeout values will be summed together."}, "@aws-cdk/aws-appsync:appSyncGraphQLAPIScopeLambdaPermission": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, a Lambda authorizer Permission created when using GraphqlApi will be properly scoped with a SourceArn."}, "@aws-cdk/aws-rds:setCorrectValueForDatabaseInstanceReadReplicaInstanceResourceId": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the value of property `instanceResourceId` in construct `DatabaseInstanceReadReplica` will be set to the correct value which is `DbiResourceId` instead of currently `DbInstanceArn`"}, "@aws-cdk/core:cfnIncludeRejectComplexResourceUpdateCreatePolicyIntrinsics": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, CFN templates added with `cfn-include` will error if the template contains Resource Update or Create policies with CFN Intrinsics that include non-primitive values."}, "@aws-cdk/aws-lambda-nodejs:sdkV3ExcludeSmithyPackages": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, both `@aws-sdk` and `@smithy` packages will be excluded from the Lambda Node.js 18.x runtime to prevent version mismatches in bundled applications."}, "@aws-cdk/aws-stepfunctions-tasks:fixRunEcsTaskPolicy": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the resource of IAM Run Ecs policy generated by SFN EcsRunTask will reference the definition, instead of constructing ARN."}, "@aws-cdk/aws-ec2:bastionHostUseAmazonLinux2023ByDefault": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the BastionHost construct will use the latest Amazon Linux 2023 AMI, instead of Amazon Linux 2."}, "@aws-cdk/core:aspectStabilization": {"recommendedValue": true, "explanation": "When enabled, a stabilization loop will be run when invoking Aspects during synthesis.", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/aws-route53-targets:userPoolDomainNameMethodWithoutCustomResource": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, use a new method for DNS Name of user pool domain target without creating a custom resource."}, "@aws-cdk/aws-elasticloadbalancingV2:albDualstackWithoutPublicIpv4SecurityGroupRulesDefault": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the default security group ingress rules will allow IPv6 ingress from anywhere"}, "@aws-cdk/aws-iam:oidcRejectUnauthorizedConnections": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the default behaviour of OIDC provider will reject unauthorized connections"}, "@aws-cdk/core:enableAdditionalMetadataCollection": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, CDK will expand the scope of usage data collected to better inform CDK development and improve communication for security concerns and emerging issues."}, "@aws-cdk/aws-lambda:createNewPoliciesWithAddToRolePolicy": {"userValue": false, "recommendedValue": false, "explanation": "[Deprecated] When enabled, Lambda will create new inline policies with AddToRolePolicy instead of adding to the Default Policy Statement"}, "@aws-cdk/aws-s3:setUniqueReplicationRoleName": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, CDK will automatically generate a unique role name that is used for s3 object replication."}, "@aws-cdk/pipelines:reduceStageRoleTrustScope": {"recommendedValue": true, "explanation": "Remove the root account principal from Stage addActions trust policy", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/aws-events:requireEventBusPolicySid": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, grantPutEventsTo() will use resource policies with Statement IDs for service principals."}, "@aws-cdk/core:aspectPrioritiesMutating": {"userValue": true, "recommendedValue": true, "explanation": "When set to true, Aspects added by the construct library on your behalf will be given a priority of MUTATING."}, "@aws-cdk/aws-dynamodb:retainTableReplica": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, table replica will be default to the removal policy of source table unless specified otherwise."}, "@aws-cdk/cognito:logUserPoolClientSecretValue": {"recommendedValue": false, "explanation": "When disabled, the value of the user pool client secret will not be logged in the custom resource lambda function logs."}, "@aws-cdk/pipelines:reduceCrossAccountActionRoleTrustScope": {"recommendedValue": true, "explanation": "When enabled, scopes down the trust policy for the cross-account action role", "unconfiguredBehavesLike": {"v2": true}}, "@aws-cdk/aws-stepfunctions:useDistributedMapResultWriterV2": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the resultWriterV2 property of DistributedMap will be used insted of resultWriter"}, "@aws-cdk/s3-notifications:addS3TrustKeyPolicyForSnsSubscriptions": {"userValue": true, "recommendedValue": true, "explanation": "Add an S3 trust policy to a KMS key resource policy for SNS subscriptions."}, "@aws-cdk/aws-ec2:requirePrivateSubnetsForEgressOnlyInternetGateway": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, the EgressOnlyGateway resource is only created if private subnets are defined in the dual-stack VPC."}, "@aws-cdk/aws-ec2-alpha:useResourceIdForVpcV2Migration": {"recommendedValue": false, "explanation": "When enabled, use resource IDs for VPC V2 migration"}, "@aws-cdk/aws-s3:publicAccessBlockedByDefault": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, setting any combination of options for BlockPublicAccess will automatically set true for any options not defined."}, "@aws-cdk/aws-lambda:useCdkManagedLogGroup": {"userValue": true, "recommendedValue": true, "explanation": "When enabled, CDK creates and manages loggroup for the lambda function"}}}}}, "minimumCliVersion": "2.1029.1"}