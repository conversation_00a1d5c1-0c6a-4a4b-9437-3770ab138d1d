import aws_cdk as core
import aws_cdk.assertions as assertions
import pytest

from infra.infra_stack import InfraStack


@pytest.fixture
def template():
    """Create CDK template for testing"""
    app = core.App()
    stack = InfraStack(app, "TestStack")
    return assertions.Template.from_stack(stack)


def test_s3_bucket_created(template):
    """Test that S3 bucket is created with correct properties"""
    template.has_resource_properties(
        "AWS::S3::Bucket",
        {
            "VersioningConfiguration": {"Status": "Enabled"},
            "BucketEncryption": {
                "ServerSideEncryptionConfiguration": [
                    {"ServerSideEncryptionByDefault": {"SSEAlgorithm": "AES256"}}
                ]
            },
            "PublicAccessBlockConfiguration": {
                "BlockPublicAcls": True,
                "BlockPublicPolicy": True,
                "IgnorePublicAcls": True,
                "RestrictPublicBuckets": True,
            },
        },
    )


def test_s3_lifecycle_rules(template):
    """Test that S3 lifecycle rules are configured"""
    template.has_resource_properties(
        "AWS::S3::Bucket",
        {
            "LifecycleConfiguration": {
                "Rules": [
                    {
                        "Id": "DeleteIncompleteMultipartUploads",
                        "Status": "Enabled",
                        "AbortIncompleteMultipartUpload": {"DaysAfterInitiation": 1},
                    }
                ]
            }
        },
    )


def test_s3_cors_configuration(template):
    """Test that S3 CORS is properly configured"""
    template.has_resource_properties(
        "AWS::S3::Bucket",
        {
            "CorsConfiguration": {
                "CorsRules": [
                    {
                        "AllowedMethods": ["GET", "PUT", "POST", "HEAD"],
                        "AllowedOrigins": [
                            "http://localhost:5173",
                            "http://localhost:3000",
                            "http://localhost:8000",
                        ],
                        "AllowedHeaders": ["*"],
                        "ExposedHeaders": [
                            "ETag",
                            "x-amz-server-side-encryption",
                            "x-amz-request-id",
                            "x-amz-id-2",
                        ],
                        "MaxAge": 3600,
                    }
                ]
            }
        },
    )


def test_iam_role_created(template):
    """Test that IAM role is created for backend service"""
    template.has_resource_properties(
        "AWS::IAM::Role",
        {
            "AssumeRolePolicyDocument": {
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Principal": {"Service": "ec2.amazonaws.com"},
                        "Action": "sts:AssumeRole",
                    }
                ]
            }
        },
    )


def test_iam_policies_attached(template):
    """Test that correct IAM policies are attached to the role"""
    template.has_resource_properties(
        "AWS::IAM::Policy",
        {
            "PolicyDocument": {
                "Statement": assertions.Match.array_with(
                    [
                        assertions.Match.object_like(
                            {
                                "Effect": "Allow",
                                "Action": assertions.Match.array_with(
                                    [
                                        "s3:GetObject*",
                                        "s3:DeleteObject*",
                                    ]
                                ),
                            }
                        )
                    ]
                )
            }
        },
    )


def test_stack_tags(template):
    """Test that stack has proper tags"""
    template.has_resource(
        "AWS::S3::Bucket",
        {
            "Properties": assertions.Match.any_value(),
            "UpdateReplacePolicy": "Retain",
            "DeletionPolicy": "Retain",
        },
    )


def test_outputs_created(template):
    """Test that CloudFormation outputs are created"""
    outputs = template.find_outputs("*")
    assert any("BucketName" in key for key in outputs.keys())
    assert any("BucketArn" in key for key in outputs.keys())
    assert any("BackendRoleArn" in key for key in outputs.keys())
