../../../bin/black,sha256=pVEpy7a4JzjXNN62HtFra-G6LPnYUz5bhFlruhqRCx0,273
../../../bin/blackd,sha256=ORkqvezoX-ePkKH4dVztOTFzBH-4jDk66FjWRfk1flY,274
30fcd23745efe32ce681__mypyc.cpython-312-darwin.so,sha256=EQpWS3cyqiTLgLw-_brqMyOzNfXiKAuPpNbeqDo-RkA,4164488
__pycache__/_black_version.cpython-312.pyc,,
_black_version.py,sha256=ZBYJc8jaUTKzcGSB-O8ED0mM59V52ravDezMWDwKWX8,20
black-24.10.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
black-24.10.0.dist-info/METADATA,sha256=h4w4yuADFv14JY4lrcEr6NFHHl3VKuOnv68ZzFjGbtY,79162
black-24.10.0.dist-info/RECORD,,
black-24.10.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black-24.10.0.dist-info/WHEEL,sha256=KRSkg8aSfz6RYrG5Yp2UIUNnkklXVfRwnmZHGnWPGgU,107
black-24.10.0.dist-info/entry_points.txt,sha256=XTCA4X2yVA0tMiV7l96Gv9TyxhVhoCaznLN2XThqYSA,144
black-24.10.0.dist-info/licenses/AUTHORS.md,sha256=q4LhA36Sf7X6e5xzVq7dFClyVKdxK2z7gpos_YsGrIg,8149
black-24.10.0.dist-info/licenses/LICENSE,sha256=nAQo8MO0d5hQz1vZbhGqqK_HLUqG1KNiI9erouWNbgA,1080
black/__init__.cpython-312-darwin.so,sha256=C9o8vfy6OIaaY6kvirXas4-0R2FAxZ8ruQThE4Y7Vm0,8616
black/__init__.py,sha256=56L1EoKDJoBxlAPXRBvRNOiv7L9PbnakAwPyupWeX5Q,51362
black/__main__.py,sha256=mogeA4o9zt4w-ufKvaQjSEhtSgQkcMVLK9ChvdB5wH8,47
black/__pycache__/__init__.cpython-312.pyc,,
black/__pycache__/__main__.cpython-312.pyc,,
black/__pycache__/_width_table.cpython-312.pyc,,
black/__pycache__/brackets.cpython-312.pyc,,
black/__pycache__/cache.cpython-312.pyc,,
black/__pycache__/comments.cpython-312.pyc,,
black/__pycache__/concurrency.cpython-312.pyc,,
black/__pycache__/const.cpython-312.pyc,,
black/__pycache__/debug.cpython-312.pyc,,
black/__pycache__/files.cpython-312.pyc,,
black/__pycache__/handle_ipynb_magics.cpython-312.pyc,,
black/__pycache__/linegen.cpython-312.pyc,,
black/__pycache__/lines.cpython-312.pyc,,
black/__pycache__/mode.cpython-312.pyc,,
black/__pycache__/nodes.cpython-312.pyc,,
black/__pycache__/numerics.cpython-312.pyc,,
black/__pycache__/output.cpython-312.pyc,,
black/__pycache__/parsing.cpython-312.pyc,,
black/__pycache__/ranges.cpython-312.pyc,,
black/__pycache__/report.cpython-312.pyc,,
black/__pycache__/rusty.cpython-312.pyc,,
black/__pycache__/schema.cpython-312.pyc,,
black/__pycache__/strings.cpython-312.pyc,,
black/__pycache__/trans.cpython-312.pyc,,
black/_width_table.cpython-312-darwin.so,sha256=LaFaT4dgoce0Y2Fi9bhVH19ORGo4s0KrzukvoFNut6g,8632
black/_width_table.py,sha256=3qJd3E9YehKhRowZzBOfTv5Uv9gnFX-cAi4ydwxu0q0,10748
black/brackets.cpython-312-darwin.so,sha256=-LDRu-rgmJ4TW_j4V2qXPDujd8z0LM0LnitLUZPjZPk,8624
black/brackets.py,sha256=292o6wQa8kXKRUO6rL08Q8bV3wLUPnxvTmPGhR5-rJo,12402
black/cache.cpython-312-darwin.so,sha256=_FaPWqWryb18d2vd2Wcp1NxfyHkPL7DMK0pKZ3DIGH4,8616
black/cache.py,sha256=XvIJW-Mi6JZeAydK9jJyJU0_Ufgwota_0jtTdBS75Es,4727
black/comments.cpython-312-darwin.so,sha256=yTmIdXrXf32nsO5LkRJlsMma_07dCUElczIGRDP9YNc,8624
black/comments.py,sha256=D8O3Ii_tXjxLljL6eAtMYRvfFc7LlOrhxOCWGdTVnlY,15942
black/concurrency.py,sha256=BNbocRQXyGzLp0SaglwzMOCtZP7RKLYYXTgkRMz-yJc,6405
black/const.cpython-312-darwin.so,sha256=r00Hcvm1MnOFTMOc1-ELTEXm_-fFH37m4_KqssUz5MI,8616
black/const.py,sha256=U7cDnhWljmrieOtPBUdO2Vcz69J_VXB6-Br94wuCVuo,321
black/debug.py,sha256=8F-Y7652nfiP_7YIeHsZ7aLGtGgm8Q9rSld_HRN_jZw,1900
black/files.py,sha256=Qnnn3TLhF9oNh1xBpOCLMNRx4YGKfcoO6HbHhrpV57E,14718
black/handle_ipynb_magics.cpython-312-darwin.so,sha256=skyvJWjQmLmXqX25wnUHsOA1QADXtlrNJ-KMWz3HAts,8648
black/handle_ipynb_magics.py,sha256=QmrPK5mIWJVu1YNmx6ycCEFpIdj6VdeOCHWAQNTHWdM,15064
black/linegen.cpython-312-darwin.so,sha256=phA1ns5EAb6rTxMscUXp7rg7WGFLksL_8zKytKJXidI,8624
black/linegen.py,sha256=pPxzgRu1PxX2srcfKcyJnpwkDmIWlTNxSS37RMIOr6A,69529
black/lines.cpython-312-darwin.so,sha256=noAxBiBvYFSX1uJm1Y5m8E4VC0n57lDI6qowgWbLbMQ,8616
black/lines.py,sha256=1k1MIzI0c3ICBwiDu7PU7yzDCWaWgjitFiXEb3lJ-Wo,39428
black/mode.cpython-312-darwin.so,sha256=dvzWeiGUZAN-UH1lkkhgdFbNXTTQb7jj6mantC2-Pbo,8616
black/mode.py,sha256=Ds7oM7_qetBbX4X55Rph_MLL1IDigDidnwGrnPzP0Ck,9512
black/nodes.cpython-312-darwin.so,sha256=Fixokn-nJoiSiAPO-mUgeF1fExrmQ6VUplR6Ah_1gIk,8616
black/nodes.py,sha256=byuFgeK0OirL0ZjVbyZU8O6pkMTy4mgCqs1eh18fJms,29945
black/numerics.cpython-312-darwin.so,sha256=_X6bSsu-dFSXNm5ZoWeXYgJXlhQ01TEy5FKbCnMEyHU,8624
black/numerics.py,sha256=xRGnTSdMVbTaA9IGechc8JM-cIuJGCc326s71hkXJIw,1655
black/output.py,sha256=z8vs-bWADomxHav4sy_YpX_QpauGFp5SXr3Gj7kQDKI,3933
black/parsing.cpython-312-darwin.so,sha256=tyaouB8NJ3w56q3iI0qC_OQW9y2KJfwk-zCc23XV63I,8624
black/parsing.py,sha256=GonhVuBEHuRLfimZFB4TUDOPCX1iXvHsPvfv8zauyCk,8612
black/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/ranges.cpython-312-darwin.so,sha256=8YiJu59fJZD_x2O5XUUOaOI6JMOoWksZxpfCr_vD2do,8616
black/ranges.py,sha256=9Lr1RbMAYck9G43amiyx5OVBkRlverikx5czxxk7SOM,19677
black/report.py,sha256=igkNi8iR5FqSa1sXddS-HnoqW7Cq7PveCmFCfd-pN6w,3452
black/resources/__init__.cpython-312-darwin.so,sha256=FdBOUsFE7cwf3IairmR6PlqBHhj8rw_fEUsyLW_4Yd4,8632
black/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/resources/__pycache__/__init__.cpython-312.pyc,,
black/resources/black.schema.json,sha256=Oe9e4UbOSa6OxZucTleTeCdaXneq1IAROkQQvmBc40Y,7506
black/rusty.cpython-312-darwin.so,sha256=CxBNNwYeQDm7I4vcYbm7Yeey0nUATwf03K2dd1fZbBg,8616
black/rusty.py,sha256=4LKo3KTUWYZ2cN6QKmwwZVbsCNt2fpu5lzue2V-uxIA,557
black/schema.cpython-312-darwin.so,sha256=amKL1tKfRFOLEdiijKcJJjh1I0esQ6TEpOJdEDI_SoQ,8616
black/schema.py,sha256=ru0z9EA-f3wfLLVPefeFpM0s4GYsYg_UjpOiMLhjdbA,431
black/strings.cpython-312-darwin.so,sha256=5NrxaIx1CiGwHBvO2SyOUssEwxnUxc5Z6eLVBzTAzoE,8624
black/strings.py,sha256=3lvO6mphXIFoipqHs2sO0W0p-lz2PyJpO89a8WM2FTA,13159
black/trans.cpython-312-darwin.so,sha256=SE_oZ5RApg2SWsttHCkvmsNDdJ1UOU1k86G4hWoT0rs,8616
black/trans.py,sha256=tnT4vYNEmrvBDPB4mF-FlegXvul_OVM9PNwUgcfPgu4,95436
blackd/__init__.py,sha256=6qzDfK3c5GQheHID48uidLZV1fN5LFwjVIU04R-IJJo,8837
blackd/__main__.py,sha256=L4xAcDh1K5zb6SsJB102AewW2G13P9-w2RiEwuFj8WA,37
blackd/__pycache__/__init__.cpython-312.pyc,,
blackd/__pycache__/__main__.cpython-312.pyc,,
blackd/__pycache__/middlewares.cpython-312.pyc,,
blackd/middlewares.py,sha256=EY_cHzcCAIkalOev2OQ8ffB7FgZPKTaBcnzvcdkidzA,1163
blib2to3/Grammar.txt,sha256=zjM1rSC9GJjnboYyRDZyKx2IPWDkscVdodwQpDCs4So,11700
blib2to3/LICENSE,sha256=V4mIG4rrnJH1g19bt8q-hKD-zUuyvi9UyeaVenjseZ0,12762
blib2to3/PatternGrammar.txt,sha256=7lul2ztnIqDi--JWDrwciD5yMo75w7TaHHxdHMZJvOM,793
blib2to3/README,sha256=QYZYIfb1NXTTYqDV4kn8oRcNG_qlTFYH1sr3V1h65ko,1074
blib2to3/__init__.py,sha256=9_8wL9Scv8_Cs8HJyJHGvx1vwXErsuvlsAqNZLcJQR0,8
blib2to3/__pycache__/__init__.cpython-312.pyc,,
blib2to3/__pycache__/pygram.cpython-312.pyc,,
blib2to3/__pycache__/pytree.cpython-312.pyc,,
blib2to3/pgen2/__init__.py,sha256=hY6w9QUzvTvRb-MoFfd_q_7ZLt6IUHC2yxWCfsZupQA,143
blib2to3/pgen2/__pycache__/__init__.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/conv.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/driver.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/grammar.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/literals.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/parse.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/pgen.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/token.cpython-312.pyc,,
blib2to3/pgen2/__pycache__/tokenize.cpython-312.pyc,,
blib2to3/pgen2/conv.cpython-312-darwin.so,sha256=kpGjskrXa_Dab1zlwjhktOjT9b9-ey3Pf0wTqDnjteo,8616
blib2to3/pgen2/conv.py,sha256=vH8a_gkalWRNxuNPRxkoigw8_UobdHHSw-PyUcUuH8I,9587
blib2to3/pgen2/driver.cpython-312-darwin.so,sha256=f0SZdY2eG8PvOQBfvm1cOadAN9jGtZwS59gbAP5dv8w,8616
blib2to3/pgen2/driver.py,sha256=IJ52vvzVaDFSOhEnIGuhpc8Y7hEUDsNzwj1fdsAXacI,10819
blib2to3/pgen2/grammar.cpython-312-darwin.so,sha256=vl8mOdvJhLRSzTGelDQ_aCJxVMaJ5toHi_s6utF4BgE,8624
blib2to3/pgen2/grammar.py,sha256=xApSZeigr9IBog2G9_vLvhOiKqUjZrQOPHlrieCw8lE,6846
blib2to3/pgen2/literals.cpython-312-darwin.so,sha256=LjIZ2bA3dC6XiI9e7NmGA7eKiSgaom6qKgxYVPpQSSs,8624
blib2to3/pgen2/literals.py,sha256=CJNuV9aTzuh5VWIGEfc8uoJpdzg_ZBvyNv6Gm7niQo8,1608
blib2to3/pgen2/parse.cpython-312-darwin.so,sha256=8CD3TtKtJ1r4wTUCWDultmZsx8VvpjH5iS1c7rWvdNw,8616
blib2to3/pgen2/parse.py,sha256=rBGw7LFYUMs5CRGgxV2bFLcDOCnOyPwSbFdnEnETHRI,15585
blib2to3/pgen2/pgen.cpython-312-darwin.so,sha256=r_EyJTWL-9XeCzqmUpCMY1SwTH8x3IcMTQwc3aPEovI,8616
blib2to3/pgen2/pgen.py,sha256=Y5D0VncRYPO0ahB8ripMwnIn_f8vdCmQL8hTC2QMSfA,15364
blib2to3/pgen2/token.cpython-312-darwin.so,sha256=GWdyjuoiwppwCqGdX7_4ZVklcUHeDdskMOZcXvqwq_s,8616
blib2to3/pgen2/token.py,sha256=pb5cvttERocFGRWjJ5C1f_a0S4C_UKmTfHXMqyFKoig,1893
blib2to3/pgen2/tokenize.cpython-312-darwin.so,sha256=KyzA6MGODnbd3aolAtGj0kSHBgxNaEzXHAyzBRezY0w,8624
blib2to3/pgen2/tokenize.py,sha256=-DaXBabR_YOoR4Mt6mPiRK67MjKEjQtW4xYpol6wrCE,41457
blib2to3/pygram.cpython-312-darwin.so,sha256=_wpYTaVS1c4cjkHmNo5GgUkKFDTL4kd4kpZVkhXkxKo,8616
blib2to3/pygram.py,sha256=l2qw7mw8I533KGWAXUFCXPGCN5F66hvYg86g-EA9GEg,4915
blib2to3/pytree.cpython-312-darwin.so,sha256=I7Hr875jP4Iq4bESCuOG1DvVcsg5wETaz1W0cYLFB7k,8616
blib2to3/pytree.py,sha256=cqoDcSnYnXkDJg9tDruWUDWsjz1qnq08y2DQeDm2lG8,32597
