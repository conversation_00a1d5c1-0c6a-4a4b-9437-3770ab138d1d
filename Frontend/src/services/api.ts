import axios from "axios";
import { API_CONFIG } from "../constants";

const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

export interface PresignedUrlRequest {
  filename: string;
  filesize: number;
}

export interface PresignedUrlResponse {
  url: string;
  key: string;
  expires_in: number;
}

export interface FileInfo {
  key: string;
  filename: string;
  size: number;
  last_modified: string;
  etag?: string;
}

export interface FileListResponse {
  files: FileInfo[];
  total_count: number;
  total_size: number;
}

export interface FileDeleteResponse {
  success: boolean;
  message: string;
  deleted_key: string;
}

export const fileService = {
  async getPresignedUrl(request: PresignedUrlRequest): Promise<PresignedUrlResponse> {
    const response = await api.post<PresignedUrlResponse>(`${API_CONFIG.ENDPOINTS.FILES}/generate-presigned-url`, request);
    return response.data;
  },

  async uploadFile(file: File, presignedUrl: string, onProgress?: (percent: number) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.open("PUT", presignedUrl);
      xhr.setRequestHeader("Content-Type", "application/octet-stream");

      xhr.upload.onprogress = (event: ProgressEvent) => {
        if (event.lengthComputable && onProgress) {
          const percent = Math.round((event.loaded / event.total) * 100);
          onProgress(percent);
        }
      };

      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve();
        } else {
          reject(new Error(`Upload failed with status: ${xhr.status}`));
        }
      };

      xhr.onerror = () => reject(new Error("Network error during upload"));
      xhr.send(file);
    });
  },

  async listFiles(): Promise<FileListResponse> {
    const response = await api.get<FileListResponse>(`${API_CONFIG.ENDPOINTS.FILES}/list`);
    return response.data;
  },

  async deleteFile(key: string): Promise<FileDeleteResponse> {
    const response = await api.delete<FileDeleteResponse>(`${API_CONFIG.ENDPOINTS.FILES}/delete`, { data: { key } });
    return response.data;
  },

  async getDownloadUrl(key: string): Promise<{ download_url: string; filename: string }> {
    const response = await api.get(`${API_CONFIG.ENDPOINTS.FILES}/download/${encodeURIComponent(key)}`);
    return response.data;
  },
};

export default api;
