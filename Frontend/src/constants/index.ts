export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  ENDPOINTS: {
    FILES: '/files',
  },
};

export const FILE_CONFIG = {
  MAX_SIZE_BYTES: 1024 * 1024 * 1024, // 1GB
  MAX_SIZE_MB: 1024, // 1GB in MB
  ALLOWED_EXTENSIONS: ['.edf'],
  UPLOAD_SUCCESS_DELAY_MS: 2000,
};

export const UI_CONFIG = {
  NOTIFICATION_AUTO_HIDE_MS: 3000,
  CONFIRM_DIALOG_ANIMATION_MS: 200,
};

export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: `File size exceeds ${FILE_CONFIG.MAX_SIZE_MB} MB limit`,
  INVALID_FILE_TYPE: `Invalid file type. Only ${FILE_CONFIG.ALLOWED_EXTENSIONS.join(', ')} files are allowed`,
  UPLOAD_FAILED: 'Failed to upload file. Please try again.',
  DELETE_FAILED: 'Failed to delete file. Please try again.',
  F<PERSON>CH_FAILED: 'Failed to fetch files. Please try again.',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
};