from datetime import datetime
import uuid

EDF_FILES_PREFIX = "edf-files"


def generate_file_key(filename: str) -> str:
    """Generate a unique S3 key for a file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = uuid.uuid4().hex[:8]
    return f"{EDF_FILES_PREFIX}/{timestamp}_{unique_id}_{filename}"


def extract_filename_from_key(key: str) -> str:
    """Extract the original filename from an S3 key"""
    return key.split("/")[-1]