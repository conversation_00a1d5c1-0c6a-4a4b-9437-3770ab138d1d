from fastapi import HTTPException, status
import logging

logger = logging.getLogger(__name__)


def handle_http_error(error_type: str, message: str, status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR):
    """Standardized HTTP error handler"""
    logger.error(f"{error_type}: {message}")
    raise HTTPException(
        status_code=status_code,
        detail=message
    )


def handle_s3_error(operation: str, error: Exception, key: str = None):
    """Handle S3-specific errors"""
    error_msg = f"Failed to {operation}"
    if key:
        error_msg += f" for {key}"
    error_msg += f": {str(error)}"
    
    logger.error(f"S3 Error - {error_msg}", exc_info=True)
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=error_msg
    )


def handle_not_found(resource: str, identifier: str):
    """Handle resource not found errors"""
    message = f"{resource} not found: {identifier}"
    logger.warning(message)
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=message
    )