from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    aws_profile: str = "biormika"
    aws_region: str = "us-east-1"
    s3_bucket_name: str = "biormikastack-s3storageedfstoragebucket8b55a415-vt2ocke56liv"
    allowed_origins: List[str] = [
        "http://localhost:5173", "http://localhost:3000"]
    max_file_size_mb: int = 1024
    allowed_file_extensions: List[str] = [".edf", ".EDF"]
    presigned_url_expiry_seconds: int = 3600

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        os.environ["AWS_PROFILE"] = self.aws_profile


settings = Settings()
