from fastapi import APIRouter
from typing import Optional

from ..models import (
    PresignedUrlRequest,
    PresignedUrlResponse,
    FileListResponse,
    FileDeleteRequest,
    FileDeleteResponse,
    ErrorResponse
)
from ..services.s3_service import s3_service, DEFAULT_EXPIRY_SECONDS
from ..config import settings
from ..utils.error_handlers import handle_s3_error, handle_not_found
from ..utils.file_utils import generate_file_key, extract_filename_from_key


router = APIRouter(prefix="/files", tags=["files"])


@router.post(
    "/generate-presigned-url",
    response_model=PresignedUrlResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid file type or size"},
        500: {"model": ErrorResponse, "description": "Server error"}
    }
)
async def generate_presigned_url(request: PresignedUrlRequest):
    """Generate a presigned URL for uploading an EDF file to S3"""
    try:
        key = generate_file_key(request.filename)
        url = s3_service.generate_presigned_url(key, request.filename)

        return PresignedUrlResponse(
            url=url,
            key=key,
            expires_in=settings.presigned_url_expiry_seconds
        )
    except Exception as e:
        handle_s3_error("generate presigned URL", e)


@router.get(
    "/list",
    response_model=FileListResponse,
    responses={
        500: {"model": ErrorResponse, "description": "Server error"}
    }
)
async def list_files(prefix: Optional[str] = None):
    """List all EDF files in the S3 bucket"""
    try:
        files = s3_service.list_files(prefix)
        total_size = sum(file.size for file in files)

        return FileListResponse(
            files=files,
            total_count=len(files),
            total_size=total_size
        )
    except Exception as e:
        handle_s3_error("list files", e)


@router.delete(
    "/delete",
    response_model=FileDeleteResponse,
    responses={
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"}
    }
)
async def delete_file(request: FileDeleteRequest):
    """Delete an EDF file from S3"""
    try:
        metadata = s3_service.get_file_metadata(request.key)
        if not metadata:
            handle_not_found("File", request.key)

        success = s3_service.delete_file(request.key)

        return FileDeleteResponse(
            success=success,
            message="File deleted successfully",
            deleted_key=request.key
        )
    except Exception as e:
        if hasattr(e, 'status_code'):
            raise
        handle_s3_error("delete file", e, request.key)


@router.get(
    "/download/{key:path}",
    responses={
        200: {"description": "Download URL"},
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"}
    }
)
async def get_download_url(key: str):
    """Generate a presigned URL for downloading a file"""
    try:
        metadata = s3_service.get_file_metadata(key)
        if not metadata:
            handle_not_found("File", key)

        url = s3_service.generate_download_url(key)

        return {
            "download_url": url,
            "expires_in": DEFAULT_EXPIRY_SECONDS,
            "filename": extract_filename_from_key(key),
            "size": metadata["size"]
        }
    except Exception as e:
        if hasattr(e, 'status_code'):
            raise
        handle_s3_error("generate download URL", e, key)
